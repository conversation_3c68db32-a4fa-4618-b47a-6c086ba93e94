import SwiftUI

/// 简化的通用导航栏组件
///
/// 直接参数传递，无需ViewModel，支持返回按钮、标题和可选的右侧按钮。
struct NavigationBarKit: View {

  // MARK: - Properties

  let title: String
  let onBack: () -> Void
  let rightButton: RightButton?
  let style: Style
  let showBackButton: Bool

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Initialization

  /// 基础初始化
  /// - Parameters:
  ///   - title: 标题文本
  ///   - onBack: 返回按钮动作
  ///   - rightButton: 右侧按钮配置
  ///   - style: 导航栏样式
  ///   - showBackButton: 是否显示返回按钮
  init(
    title: String,
    onBack: @escaping () -> Void,
    rightButton: RightButton? = nil,
    style: Style = .normal,
    showBackButton: Bool = true
  ) {
    self.title = title
    self.onBack = onBack
    self.rightButton = rightButton
    self.style = style
    self.showBackButton = showBackButton
  }

  /// 只有返回按钮的导航栏
  /// - Parameters:
  ///   - title: 标题文本
  ///   - onBack: 返回按钮动作
  init(title: String, onBack: @escaping () -> Void) {
    self.init(title: title, onBack: onBack, rightButton: nil, style: .normal, showBackButton: true)
  }

  /// 带图标按钮的导航栏
  /// - Parameters:
  ///   - title: 标题文本
  ///   - onBack: 返回按钮动作
  ///   - rightIcon: 右侧图标名称
  ///   - onRight: 右侧按钮动作
  ///   - rightStyle: 右侧按钮样式
  init(
    title: String,
    onBack: @escaping () -> Void,
    rightIcon: String,
    onRight: @escaping () -> Void,
    rightStyle: ButtonStyle = .normal
  ) {
    self.init(
      title: title,
      onBack: onBack,
      rightButton: .icon(rightIcon, style: rightStyle, action: onRight),
      style: .normal,
      showBackButton: true
    )
  }

  /// 带文本按钮的导航栏
  /// - Parameters:
  ///   - title: 标题文本
  ///   - onBack: 返回按钮动作
  ///   - rightText: 右侧文本
  ///   - onRight: 右侧按钮动作
  ///   - rightStyle: 右侧按钮样式
  init(
    title: String,
    onBack: @escaping () -> Void,
    rightText: String,
    onRight: @escaping () -> Void,
    rightStyle: ButtonStyle = .primary
  ) {
    self.init(
      title: title,
      onBack: onBack,
      rightButton: .text(rightText, style: rightStyle, action: onRight),
      style: .normal,
      showBackButton: true
    )
  }

  // MARK: - Body

  var body: some View {
    HStack {
      // 返回按钮
      if showBackButton {
        NavigationButton(
          icon: "chevron-left",
          action: {
            dataManager.hapticManager.trigger(.impactLight)
            onBack()
          },
          style: .back
        )
        .transition(.move(edge: .leading).combined(with: .opacity))
      }

      Spacer()

      // 标题
      Text(title)
        .font(style.titleFont)
        .foregroundColor(style.titleColor)

      Spacer()

      // 右侧按钮或占位空间
      if let rightButton = rightButton {
        NavigationButton(
          icon: rightButton.icon,
          text: rightButton.text,
          action: {
            dataManager.hapticManager.trigger(.impactLight)
            rightButton.action()
          },
          style: rightButton.style.navigationButtonStyle
        )
        .transition(.move(edge: .trailing).combined(with: .opacity))
      } else {
        // 占位空间，保持标题居中
        Color.clear
          .frame(width: 46, height: 46)
      }
    }
    .padding(.horizontal, style.horizontalPadding)
    .padding(.vertical, style.verticalPadding)
  }
}

// MARK: - 导航按钮组件

/// 导航栏按钮组件
struct NavigationButton: View {
  let icon: String?
  let text: String?
  let action: () -> Void
  let style: NavigationButtonStyle

  init(
    icon: String? = nil,
    text: String? = nil,
    action: @escaping () -> Void,
    style: NavigationButtonStyle
  ) {
    self.icon = icon
    self.text = text
    self.action = action
    self.style = style
  }

  var body: some View {
    Button(action: action) {
      Group {
        if let text = text {
          Text(text)
            .font(style.textFont)
            .foregroundColor(style.foregroundColor)
        } else if let icon = icon {
          Image(icon)
            .font(style.iconFont)
            .foregroundColor(style.foregroundColor)
        }
      }
      .frame(width: 46, height: 46)
      .background(style.backgroundColor)
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(style.borderColor, lineWidth: style.borderWidth)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - 简化的配置类型

/// 右侧按钮配置
struct RightButton {
  let icon: String?
  let text: String?
  let action: () -> Void
  let style: ButtonStyle

  /// 创建图标按钮
  static func icon(
    _ iconName: String,
    style: ButtonStyle = .normal,
    action: @escaping () -> Void
  ) -> RightButton {
    return RightButton(
      icon: iconName,
      text: nil,
      action: action,
      style: style
    )
  }

  /// 创建文本按钮
  static func text(
    _ text: String,
    style: ButtonStyle = .normal,
    action: @escaping () -> Void
  ) -> RightButton {
    return RightButton(
      icon: nil,
      text: text,
      action: action,
      style: style
    )
  }
}

/// 简化的导航栏样式
enum Style {
  case normal, large, compact

  var titleFont: Font {
    switch self {
    case .normal: return .system(size: 15, weight: .medium)
    case .large: return .system(size: 18, weight: .semibold)
    case .compact: return .system(size: 14, weight: .medium)
    }
  }

  var titleColor: Color {
    return .cBlack
  }

  var horizontalPadding: CGFloat {
    switch self {
    case .normal: return 16
    case .large: return 16
    case .compact: return 12
    }
  }

  var verticalPadding: CGFloat {
    switch self {
    case .normal: return 8
    case .large: return 12
    case .compact: return 6
    }
  }
}

/// 简化的按钮样式
enum ButtonStyle {
  case normal, primary, destructive

  var navigationButtonStyle: NavigationButtonStyle {
    switch self {
    case .normal: return .default
    case .primary: return .primary
    case .destructive: return .destructive
    }
  }
}

/// 导航按钮样式
struct NavigationButtonStyle {
  let backgroundColor: Color
  let foregroundColor: Color
  let borderColor: Color
  let borderWidth: CGFloat
  let iconFont: Font
  let textFont: Font

  /// 默认样式（白色背景）
  static let `default` = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cBlack,
    borderColor: .cAccentBlue.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 返回按钮样式
  static let back = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cBlack,
    borderColor: .cAccentBlue.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 主要操作按钮样式（蓝色）
  static let primary = NavigationButtonStyle(
    backgroundColor: .cAccentBlue,
    foregroundColor: .cWhite,
    borderColor: .cAccentBlue,
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 危险操作按钮样式（红色）
  static let destructive = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cAccentRed,
    borderColor: .cAccentRed.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )

  /// 禁用状态按钮样式（浅灰色）
  static let disabled = NavigationButtonStyle(
    backgroundColor: .cWhite,
    foregroundColor: .cBlack.opacity(0.3),  // 更浅的颜色
    borderColor: .cAccentBlue.opacity(0.08),
    borderWidth: 1,
    iconFont: .system(size: 16, weight: .medium),
    textFont: .system(size: 14, weight: .medium)
  )
}

// MARK: - 预览

#Preview {
  ScrollView {
    VStack(spacing: 30) {

      // MARK: 基础导航栏
      VStack(alignment: .leading, spacing: 15) {
        Text("基础导航栏")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            title: "基本导航栏",
            onBack: { print("返回") }
          )

          NavigationBarKit(
            title: "简化版导航栏",
            onBack: { print("返回") }
          )
        }
      }

      Divider()

      // MARK: 带操作按钮
      VStack(alignment: .leading, spacing: 15) {
        Text("带操作按钮")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            title: "卡包",
            onBack: { print("返回") },
            rightIcon: "plus-large",
            onRight: { print("添加") }
          )

          NavigationBarKit(
            title: "交易记录",
            onBack: { print("返回") },
            rightIcon: "plus-large",
            onRight: { print("新建交易") },
            rightStyle: .primary
          )

          NavigationBarKit(
            title: "交易详情",
            onBack: { print("返回") },
            rightIcon: "trash-can-simple",
            onRight: { print("删除交易") },
            rightStyle: .destructive
          )
        }
      }

      Divider()

      // MARK: 文本按钮
      VStack(alignment: .leading, spacing: 15) {
        Text("文本按钮")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            title: "编辑分类",
            onBack: { print("返回") },
            rightText: "保存",
            onRight: { print("保存") },
            rightStyle: .primary
          )

          NavigationBarKit(
            title: "编辑交易",
            onBack: { print("取消") },
            rightText: "保存",
            onRight: { print("保存") },
            rightStyle: .primary
          )

          NavigationBarKit(
            title: "交易类别",
            onBack: { print("返回") },
            rightText: "去重",
            onRight: { print("去重") }
          )
        }
      }

      Divider()

      // MARK: 不同样式
      VStack(alignment: .leading, spacing: 15) {
        Text("不同样式")
          .font(.title2)
          .fontWeight(.bold)

        VStack(spacing: 10) {
          NavigationBarKit(
            title: "紧凑样式",
            onBack: { print("返回") },
            rightIcon: "settings-gear-2",
            onRight: { print("设置") }
          )
          .background(.cLightBlue)

          NavigationBarKit(
            title: "大标题样式",
            onBack: { print("返回") },
            rightIcon: "plus-large",
            onRight: { print("添加") },
            rightStyle: .primary
          )
          .background(.cLightBlue)

          NavigationBarKit(
            title: "标准样式",
            onBack: { print("返回") },
            rightText: "完成",
            onRight: { print("完成") },
            rightStyle: .primary
          )
          .background(.cLightBlue)
        }
      }

    }
    .padding()
  }
  .background(Color(.systemGroupedBackground))
}
