//
//  CardDetailVM.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import Foundation
import SwiftData
import SwiftUI

/// 卡片详情覆盖视图模型
///
/// 负责卡片详情覆盖视图的业务逻辑处理和数据管理，遵循新架构的MVVM模式。
/// 该类从DataManagement获取原始数据，进行业务逻辑处理后，
/// 为CardDetailView提供格式化的、可直接使用的数据。
///
/// ## 主要职责
/// - 卡片详情数据加载和展示
/// - 卡片编辑状态和交互管理
/// - 创建/查看/编辑三种模式的状态管理
/// - 卡片动画和UI状态控制
/// - 交易数据统计和格式化
///
/// ## 模式说明
/// - **创建模式** (`isCreatingCard = true`): 创建新卡片
/// - **查看模式** (`isCreatingCard = false, isEditingCard = false`): 查看卡片详情
/// - **编辑模式** (`isCreatingCard = false, isEditingCard = true`): 编辑现有卡片
///
/// ## 数据流向
/// ```
/// DataManagement → CardDetailVM → CardDetailView
/// ```
///
/// - Important: 使用DataManagement替代AppDataManager，确保数据一致性
/// - Note: 所有UI相关的数据处理都在ViewModel层完成
/// - Author: NZUE
/// - Version: 2.0 (New架构)
/// - Since: 2025.7.17
@MainActor
final class CardDetailVM: ObservableObject {

  // MARK: - Dependencies

  /// 数据管理器，提供原始数据源
  ///
  /// 通过DataManagement获取卡片、交易、分类等基础数据，
  /// 作为所有业务逻辑计算的数据来源。
  private let dataManager: DataManagement

  /// 交易点击回调
  var onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - Published Properties

  /// 时间控制器 - 管理时间选择逻辑
  @Published var timeControlVM: TimeControlVM

  /// 当前卡片（用于响应式数据更新）
  private var currentCard: CardModel?

  // MARK: - Card UI State

  /// 卡片是否翻转
  @Published var isFlipped: Bool = false

  /// 拖拽偏移量
  @Published var dragAmount: CGFloat = 0

  /// 旋转角度
  @Published var rotationAngle: Double = 0

  /// 是否正在编辑卡片
  @Published var isEditingCard: Bool = false

  // MARK: - Card Edit State

  /// 编辑中的卡片名称
  @Published var editCardName: String = ""

  /// 编辑中的余额
  @Published var editBalance: Double = 0.0

  /// 编辑中的债务
  @Published var editDebt: Double = 0.0

  /// 编辑中的额度
  @Published var editLimit: Double = 0.0

  /// 是否包含在总计中
  @Published var editIsIncludeInTotal: Bool = true

  /// 是否可用于记账
  @Published var editIsAvailableForBookkeeping: Bool = true

  /// 选择的货币代码
  @Published var editSelectedCurrencyCode: String = ""

  /// 货币符号
  @Published var editCurrencySymbol: String = ""

  /// 选择的封面类型
  @Published var editSelectedcoverType: CardCoverType = .card1

  /// 是否为深色封面
  @Published var editisCoverDark: Bool = false

  // MARK: - Credit Card State

  /// 账单日
  @Published var editBillingDay: Int? = nil

  /// 还款日
  @Published var editRepaymentDay: Int? = nil

  /// 还款类型
  @Published var editRepaymentType: RepaymentType = .fixedDay

  /// 是否设置了账单日
  @Published var editHasBillDaySet = false

  /// 是否设置了还款日
  @Published var editHasDueDaySet = false

  // MARK: - UI Control State

  /// 显示数字键盘
  @Published var showingNumericKeypad = false

  /// 显示账单日选择器
  @Published var showingBillingDayPicker = false

  /// 显示还款日选择器
  @Published var showingRepaymentDayPicker = false

  /// 显示封面选项
  @Published var showingCoverOptions = false

  /// 显示货币选择器
  @Published var showingCurrencyPicker = false

  /// 选择的日期
  @Published var selectedDay: Int = 1

  /// 临时金额
  @Published var tempAmount: String = "0"

  /// 编辑金额类型
  @Published var editingAmount: EditingAmount?

  /// 聚焦的日期字段
  @Published var focusedDateField: DateField?

  /// 显示警告
  @Published var showingAlert = false

  /// 警告信息
  @Published var alertMessage = ""

  /// 显示卡片操作表单
  @Published var showCardActionSheet = false

  // MARK: - Card Detail Data State

  /// 是否正在加载卡片详情
  @Published var isLoadingCardDetail = false

  /// 卡片详情错误信息
  @Published var cardDetailErrorMessage: String?

  /// 收入支出统计数据
  ///
  /// 预先准备好的收入支出统计数据，包含收入、支出金额和货币符号。
  /// View层可以直接使用，无需在UI层组装数据。
  @Published var incomeExpenseData: (income: Double, expense: Double, currencySymbol: String)

  /// 交易日期分组数据（和 TransactionRecordVM 保持一致）
  ///
  /// 按日期分组的交易数据，包含每日收支统计和交易行视图模型。
  /// 直接用于 TransactionListContent 组件显示。
  @Published var transactionDayGroups: [TransactionDayGroupWithRowVM] = []

  // MARK: - Computed Properties (类似 HomeVM 的简洁风格)

  /// 当前选中的卡片（简化访问）
  var card: CardModel? {
    return currentCard
  }

  /// 时间范围（使用时间控制器）
  var dateRange: (start: Date, end: Date) {
    return timeControlVM.dateRange
  }

  // MARK: - Initialization

  /// 初始化卡片详情覆盖视图模型
  ///
  /// 创建CardDetailVM实例，直接传入数据管理器。
  ///
  /// - Parameter dataManager: 数据管理器，提供基础数据源
  init(dataManager: DataManagement) {
    self.dataManager = dataManager

    // 初始化收入支出统计数据
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    let currencySymbol = dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
    self.incomeExpenseData = (
      income: 0.0,
      expense: 0.0,
      currencySymbol: currencySymbol
    )

    // 初始化时间控制器 - 先不设置回调
    self.timeControlVM = TimeControlVM(selectedPeriod: .month, currentDate: Date()) { _, _ in }

    // 设置时间控制器的回调
    self.timeControlVM.onDateChange = { [weak self] date, period in
      self?.loadCardDetailData()
    }
  }

  // MARK: - Computed Properties

  /// 当前选择的时间周期（从时间控制器获取）
  var selectedPeriod: TransactionTimePeriod {
    return timeControlVM.selectedPeriod
  }

  /// 当前选择的日期（从时间控制器获取）
  var currentDate: Date {
    return timeControlVM.currentDate
  }

  /// 基础货币代码
  var baseCurrencyCode: String {
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 基础货币符号
  var baseCurrencySymbol: String {
    dataManager.currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
  }

  /// 所有卡片
  var allCards: [CardModel] {
    dataManager.cards
  }

  /// 所有交易
  var allTransactions: [TransactionModel] {
    dataManager.allTransactions
  }

  /// 所有货币
  var allCurrencies: [CurrencyModel] {
    dataManager.currencies
  }

  /// 基础货币数组（用于各种计算）
  var baseCurrencies: [CurrencyModel] {
    dataManager.currencies.filter { $0.isBaseCurrency }
  }

  /// 默认货币信息
  func getDefaultCurrency() -> (code: String, symbol: String) {
    defaultCurrency(baseCurrency: baseCurrencies)
  }

  /// 获取当前货币代码
  func getCurrentCurrencyCode(for card: CardModel?) -> String {
    currentCurrencyCode(card: card, baseCurrency: baseCurrencies)
  }

  /// 获取当前货币符号
  func getCurrentCurrencySymbol(for card: CardModel?) -> String {
    currentCurrencySymbol(card: card, baseCurrency: baseCurrencies)
  }

  /// 创建预览卡片
  func getPreviewCard(
    card: CardModel?,
    isCreatingCard: Bool,
    isCredit: Bool?,
    subCategory: CardSubCategoryResponse?,
    mainCategory: CardCategoryResponse?
  ) -> CardModel {
    createPreviewCard(
      card: card,
      isCreatingCard: isCreatingCard,
      isCredit: isCredit,
      subCategory: subCategory,
      mainCategory: mainCategory,
      baseCurrency: baseCurrencies
    )
  }

  /// 获取问号图标的图片数据
  private func getQuestionImageData() -> Data {
    if let image = UIImage(named: "question"),
      let data = image.pngData()
    {
      return data
    }
    return Data()
  }

  /// 获取交易分类信息
  func getCategoryInfo(for transaction: TransactionModel) -> (name: String, icon: IconType?) {
    guard let categoryId = transaction.transactionCategoryId else {
      return (name: "未知分类", icon: .image(getQuestionImageData()))
    }

    let categoryInfo = dataManager.getCategoryInfo(for: categoryId)
    return (name: categoryInfo.name, icon: categoryInfo.icon)
  }

  /// 编辑金额类型
  enum EditingAmount {
    case balance, debt, limit

    var title: String {
      switch self {
      case .balance: return "编辑余额"
      case .debt: return "编辑欠款"
      case .limit: return "编辑额度"
      }
    }
  }

  /// 日期字段类型
  enum DateField {
    case billing, repayment
  }

  /// 还款类型
  enum RepaymentType: String {
    case fixedDay = "固定日期"
    case afterBilling = "出账后"

    func displayText(day: Int?) -> String {
      guard let day = day else { return "未设置" }

      if day == 0 {
        switch self {
        case .fixedDay: return "每月最后一天"
        case .afterBilling: return "出账后0日"
        }
      }

      switch self {
      case .fixedDay: return "每月 \(day) 日"
      case .afterBilling: return "出账后 \(day) 日"
      }
    }
  }

  // MARK: - 计算属性

  /// 是否为信用卡类型
  func isCreditCard(isCreatingCard: Bool, isCredit: Bool?, card: CardModel?) -> Bool {
    isCreatingCard ? (isCredit ?? false) : (card?.isCredit ?? false)
  }

  /// 默认货币信息
  func defaultCurrency(baseCurrency: [CurrencyModel]) -> (code: String, symbol: String) {
    let code = baseCurrency.first?.code ?? "CNY"
    let symbol = baseCurrency.first?.symbol ?? "¥"
    return (code, symbol)
  }

  /// 当前货币代码
  func currentCurrencyCode(card: CardModel?, baseCurrency: [CurrencyModel]) -> String {
    editSelectedCurrencyCode.isEmpty
      ? (card?.currency ?? defaultCurrency(baseCurrency: baseCurrency).code)
      : editSelectedCurrencyCode
  }

  /// 当前货币符号
  func currentCurrencySymbol(card: CardModel?, baseCurrency: [CurrencyModel]) -> String {
    editCurrencySymbol.isEmpty
      ? (card?.symbol ?? defaultCurrency(baseCurrency: baseCurrency).symbol)
      : editCurrencySymbol
  }

  /// 卡片显示名称
  var displayCardName: String {
    editCardName.isEmpty ? "新建卡片" : editCardName
  }

  /// 分类名称
  func categoryName(subCategory: CardSubCategoryResponse?, mainCategory: CardCategoryResponse?)
    -> String
  {
    subCategory?.displayName ?? mainCategory?.name ?? "未分类"
  }

  /// 账单日显示值（用于UI显示）
  var billingDayForDisplay: Int {
    editBillingDay == 0 ? 31 : (editBillingDay ?? 1)
  }

  /// 账单日显示文本
  var billingDayDisplayText: String {
    editHasBillDaySet
      ? RepaymentType(rawValue: editRepaymentType.rawValue)?.displayText(day: editBillingDay)
        ?? "未设置" : "未设置"
  }

  /// 还款日显示文本
  var repaymentDayDisplayText: String {
    editHasDueDaySet
      ? RepaymentType(rawValue: editRepaymentType.rawValue)?.displayText(day: editRepaymentDay)
        ?? "未设置" : "未设置"
  }

  // MARK: - 公共方法

  /// 获取加载状态
  var isLoading: Bool {
    isLoadingCardDetail
  }

  /// 获取错误信息
  var errorMessage: String? {
    cardDetailErrorMessage
  }

  /// 准备显示数据（统一的数据准备入口）
  func prepareDataForDisplay(
    isCreatingCard: Bool,
    card: CardModel?,
    baseCurrency: [CurrencyModel],
    isCredit: Bool?,
    transactions: [TransactionModel],
    currencies: [CurrencyModel],
    selectedPeriod: TransactionTimePeriod,
    currentDate: Date
  ) {
    // 存储当前卡片，用于响应式更新
    self.currentCard = card

    if isCreatingCard {
      // 创建模式：加载默认数据
      loadDefaultDataForCreation(baseCurrency: baseCurrency, isCredit: isCredit)
    } else {
      // 编辑模式：加载现有数据并加载卡片详情
      loadExistingCardData(card: card)
      loadCardDetailData()
    }
  }

  /// 加载卡片详情数据（简化版本，类似 HomeVM 的风格）
  func loadCardDetailData() {
    guard card != nil else { return }

    isLoadingCardDetail = true
    cardDetailErrorMessage = nil

    // 简化的数据处理逻辑
    processData()

    isLoadingCardDetail = false
  }

  /// 处理数据的核心方法（和 TransactionRecordVM.processData 完全一致）
  private func processData() {
    guard let card = card else { return }

    // 使用综合服务一次性获取所有数据
    let result = TransactionListDataService.shared.getTransactionListWithStatistics(
      dataManager: dataManager,
      dateRange: dateRange,
      cardId: card.id,  // 🎯 指定卡片ID
      targetCurrency: card.currency,  // 🎯 使用卡片货币
      showBalance: true,  // 🎯 卡片详情页面显示余额
      includeSystemTransactions: true,  // 🎯 显示创建和调整交易（但不计入收支统计）
      onTransactionTap: onTransactionTap
    )

    // 更新UI数据
    transactionDayGroups = result.transactionDayGroups
    incomeExpenseData = (
      income: result.incomeExpenseStatistics.totalIncome,
      expense: result.incomeExpenseStatistics.totalExpense,
      currencySymbol: result.incomeExpenseStatistics.currencySymbol
    )

  }

  /// 是否为当前日期
  func isCurrentDate() -> Bool {
    Calendar.current.isDate(currentDate, inSameDayAs: Date())
  }

  /// 创建预览用的CardModel对象，基于编辑状态数据
  func createPreviewCard(
    card: CardModel?,
    isCreatingCard: Bool,
    isCredit: Bool?,
    subCategory: CardSubCategoryResponse?,
    mainCategory: CardCategoryResponse?,
    baseCurrency: [CurrencyModel]
  ) -> CardModel {
    let isCreditCard = self.isCreditCard(
      isCreatingCard: isCreatingCard, isCredit: isCredit, card: card)
    let currentCurrencyCode = self.currentCurrencyCode(card: card, baseCurrency: baseCurrency)
    let currentCurrencySymbol = self.currentCurrencySymbol(card: card, baseCurrency: baseCurrency)

    // 获取logo数据 - 创建模式使用类别图标，编辑模式保持原有图标
    let logoData: Data? = {
      if isCreatingCard {
        // 创建模式：使用类别图标
        if let subCategory = subCategory {
          return UIImage(named: subCategory.displayName)?.pngData()
        } else if let mainCategory = mainCategory {
          return UIImage(named: mainCategory.imageUrl)?.pngData()
        }
        return nil
      } else {
        // 编辑模式：保持原有图标
        return card?.bankLogo
      }
    }()

    let previewCard = CardModel(
      id: card?.id ?? UUID(),
      order: card?.order ?? 0,
      isCredit: isCreditCard,
      isSelected: true,
      name: displayCardName,
      remark: card?.remark ?? "",
      currency: currentCurrencyCode,
      symbol: currentCurrencySymbol,
      balance: editBalance,  // 信用卡和储蓄卡都直接使用余额
      credit: editLimit,
      isStatistics: false,
      cover: editSelectedcoverType.rawValue,
      bankLogo: logoData,
      bankName: subCategory?.displayName ?? mainCategory?.name ?? card?.bankName ?? "",
      cardNumber: card?.cardNumber ?? "",
      isFixedDueDay: true,
      createdAt: card?.createdAt ?? Date(),
      updatedAt: Date()
    )
    return previewCard
  }

  /// 取消卡片编辑
  func cancelCardEditing(isCreatingCard: Bool, card: CardModel?) {
    if isCreatingCard {
      // 创建模式下使用动画关闭视图
      // 这里需要在View中处理关闭逻辑
    } else {
      // 编辑模式下恢复原始数据，确保完全一致
      restoreOriginalCardData(card: card)

      // 退出编辑模式
      isEditingCard = false
    }
  }

  /// 处理数字键盘输入
  func handleNumericKeypadInput() {
    if let amount = Double(tempAmount) {
      switch editingAmount {
      case .balance:
        // 余额可以是正数（存款）或负数（欠款）
        editBalance = amount
      case .debt:
        // 不再使用欠款字段
        break
      case .limit:
        // 额度始终存储为正数
        editLimit = abs(amount)
      case .none:
        break
      }
    }
    showingNumericKeypad = false
  }

  /// 处理账单日保存
  func handleBillingDaySave() {
    editHasBillDaySet = true
    editBillingDay = selectedDay == 31 ? 0 : selectedDay
    showingBillingDayPicker = false
    focusedDateField = nil
  }

  /// 处理账单日清除
  func handleBillingDayClear() {
    editHasBillDaySet = false
    editBillingDay = nil
    showingBillingDayPicker = false
    focusedDateField = nil
  }

  /// 处理还款日保存
  func handleRepaymentDaySave() {
    editHasDueDaySet = true
    editRepaymentDay = (editRepaymentType == .fixedDay && selectedDay == 31) ? 0 : selectedDay
    showingRepaymentDayPicker = false
    focusedDateField = nil
  }

  /// 处理还款日清除
  func handleRepaymentDayClear() {
    editHasDueDaySet = false
    editRepaymentDay = nil
    showingRepaymentDayPicker = false
    focusedDateField = nil
  }

  /// 显示金额编辑键盘
  func showAmountEditKeypad(for amountType: EditingAmount, amount: Double) {
    editingAmount = amountType
    // 对于额度，显示时使用绝对值（确保显示为正数）
    // 对于余额，可以显示负数（信用卡欠款）
    let displayAmount = (amountType == .limit) ? abs(amount) : amount
    let maxDecimalPlaces = keypadMaxDecimalPlaces(for: amountType)
    tempAmount = NumberFormatService.shared.formatAmountForInput(
      displayAmount, maxDecimals: maxDecimalPlaces)
    showingNumericKeypad = true
  }

  /// 获取键盘配置：是否允许负数
  func keypadAllowsNegative(for amountType: EditingAmount) -> Bool {
    // 只有在编辑卡片余额时才允许负数（用于信用卡欠款）
    return amountType == .balance
  }

  /// 获取键盘配置：最大小数位数
  func keypadMaxDecimalPlaces(for amountType: EditingAmount) -> Int {
    // 所有金额类型都使用2位小数（汇率在其他地方处理）
    return 2
  }

  /// 显示账单日选择器
  func showBillingDayPicker() {
    selectedDay = billingDayForDisplay
    focusedDateField = .billing
    showingBillingDayPicker = true
  }

  /// 显示还款日选择器
  func showRepaymentDayPicker() {
    selectedDay = editRepaymentDay ?? 1
    focusedDateField = .repayment
    showingRepaymentDayPicker = true
  }

  /// 切换卡片翻转状态
  func toggleCardFlip() {
    withAnimation {
      isEditingCard = true
      isFlipped.toggle()
      rotationAngle = isFlipped ? 180 : 0
    }
  }

  /// 退出编辑模式
  func exitEditingMode() {
    withAnimation {
      isEditingCard = false
    }
  }

  /// 处理货币选择
  func handleCurrencySelection(code: String) {
    editSelectedCurrencyCode = code
  }

  /// 删除卡片（包含交易）
  func deleteCardWithTransactions(card: CardModel, modelContext: ModelContext) {
    do {
      // 1. 查找所有与该卡片相关的交易记录
      let descriptor = FetchDescriptor<TransactionModel>()
      let allTransactions = try modelContext.fetch(descriptor)

      // 使用 TransactionQueryService 查询卡片相关交易
      let relatedTransactions = allTransactions.filter { transaction in
        transaction.fromCardId == card.id || transaction.toCardId == card.id
      }

      // 2. 检查转账交易的完整性
      let problematicTransfers = checkTransferIntegrityForDeletion(
        cardToDelete: card,
        allTransactions: allTransactions,
        relatedTransactions: relatedTransactions
      )

      if !problematicTransfers.isEmpty {
        let transferIds = problematicTransfers.map { $0.id.uuidString.prefix(8) }.joined(
          separator: ", ")
        alertMessage =
          "无法删除卡片：存在 \(problematicTransfers.count) 个转账交易会因删除此卡片而变成无效状态。转账ID: \(transferIds)。请先删除这些转账交易。"
        showingAlert = true
        return
      }

      // 3. 删除所有相关的交易记录
      for transaction in relatedTransactions {
        modelContext.delete(transaction)
        print("[删除卡片] 删除相关交易: \(transaction.id), 类型: \(transaction.transactionType)")
      }

      // 4. 删除卡片本身
      modelContext.delete(card)

      // 5. 保存更改
      try modelContext.save()
      showCardActionSheet = false

      print("[删除卡片] 成功删除卡片 \(card.name) 及其 \(relatedTransactions.count) 个相关交易")

    } catch {
      alertMessage = "删除卡片失败：\(error.localizedDescription)"
      showingAlert = true
      print("[删除卡片] 删除失败: \(error.localizedDescription)")
    }
  }

  /// 删除卡片（仅卡片）
  func deleteCardOnly(card: CardModel, modelContext: ModelContext) {
    do {
      // 1. 查找所有与该卡片相关的交易记录
      let descriptor = FetchDescriptor<TransactionModel>()
      let allTransactions = try modelContext.fetch(descriptor)

      // 使用 TransactionQueryService 查询卡片相关交易
      let relatedTransactions = allTransactions.filter { transaction in
        transaction.fromCardId == card.id || transaction.toCardId == card.id
      }

      // 2. 找出需要删除的基准点交易（创建卡片和调整卡片）
      let basePointTransactions = relatedTransactions.filter { transaction in
        [TransactionType.createCard, .adjustCard].contains(transaction.transactionType)
      }

      // 3. 删除基准点交易（创建卡片和调整卡片的交易）
      for transaction in basePointTransactions {
        modelContext.delete(transaction)
        print("[删除卡片] 删除基准点交易: \(transaction.id), 类型: \(transaction.transactionType)")
      }

      // 4. 删除卡片本身
      modelContext.delete(card)
      try modelContext.save()
      showCardActionSheet = false

      let deletedTransactionsCount = basePointTransactions.count
      let remainingTransactions = relatedTransactions.count - deletedTransactionsCount

      if deletedTransactionsCount > 0 {
        print("[删除卡片] 成功删除卡片 \(card.name) 及其 \(deletedTransactionsCount) 个基准点交易")
        if remainingTransactions > 0 {
          print("[删除卡片] 保留 \(remainingTransactions) 个其他类型交易（将成为孤儿交易）")
        }
      } else {
        print("[删除卡片] 成功删除卡片 \(card.name)（无基准点交易）")
        if remainingTransactions > 0 {
          print("[删除卡片] 保留 \(remainingTransactions) 个交易（将成为孤儿交易）")
        }
      }

    } catch {
      alertMessage = "删除卡片失败：\(error.localizedDescription)"
      showingAlert = true
      print("[删除卡片] 删除失败: \(error.localizedDescription)")
    }
  }

  // MARK: - Private Methods

  /// 加载创建模式的默认数据
  func loadDefaultDataForCreation(baseCurrency: [CurrencyModel], isCredit: Bool?) {
    // 设置默认卡片名称为"新建卡片"（而不是空字符串）
    editCardName = "新建卡片"
    editIsIncludeInTotal = true
    editIsAvailableForBookkeeping = true

    // 设置默认货币
    let defaultCurrency = self.defaultCurrency(baseCurrency: baseCurrency)
    editSelectedCurrencyCode = defaultCurrency.code
    editCurrencySymbol = defaultCurrency.symbol

    // 设置默认背景
    editSelectedcoverType = .card1
    editisCoverDark = false

    // 根据资产类型初始化
    let isCreditCard = isCredit ?? false
    if !isCreditCard {
      editBalance = 0
    } else {
      initializeCreditCardDefaults()
    }
  }

  /// 初始化信用卡默认值
  private func initializeCreditCardDefaults() {
    editDebt = 0
    editLimit = 0
    editHasBillDaySet = false
    editHasDueDaySet = false
    editBillingDay = nil
    editRepaymentDay = nil
    editRepaymentType = .fixedDay
  }

  /// 加载现有卡片数据
  func loadExistingCardData(card: CardModel?) {
    guard let card = card else { return }

    // 基本信息
    editCardName = card.name
    editIsIncludeInTotal = card.isStatistics  // 使用isStatistics字段
    editIsAvailableForBookkeeping = card.isSelected
    editSelectedCurrencyCode = card.currency
    editCurrencySymbol = card.symbol

    // 背景设置 - 优化：一次性获取背景信息
    let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
    editSelectedcoverType = coverType
    editisCoverDark = CardCoverHelper.shared.isCoverDark(for: coverType)

    // 根据卡片类型加载特定数据
    if !card.isCredit {
      editBalance = card.balance
    } else {
      loadCreditCardData(from: card)
    }
  }

  /// 加载信用卡特有数据
  private func loadCreditCardData(from card: CardModel) {
    // 信用卡直接使用余额（可以是正数存款或负数欠款）
    editBalance = card.balance
    editDebt = 0  // 不再使用欠款字段
    editLimit = card.credit

    // 设置账单日
    editHasBillDaySet = card.billDay != nil
    editBillingDay = card.billDay

    // 设置还款日
    editHasDueDaySet = card.dueDay != nil
    editRepaymentDay = card.dueDay
    editRepaymentType = card.isFixedDueDay ? .fixedDay : .afterBilling
  }

  /// 恢复原始卡片数据，确保与原card完全一致
  private func restoreOriginalCardData(card: CardModel?) {
    guard let card = card else { return }

    // 完全恢复基本信息
    editCardName = card.name
    editIsIncludeInTotal = card.isStatistics  // 使用isStatistics字段
    editIsAvailableForBookkeeping = card.isSelected
    editSelectedCurrencyCode = card.currency
    editCurrencySymbol = card.symbol

    // 恢复背景设置 - 优化：一次性获取背景信息
    let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
    editSelectedcoverType = coverType
    editisCoverDark = CardCoverHelper.shared.isCoverDark(for: coverType)

    // 完全恢复金额信息
    if !card.isCredit {
      editBalance = card.balance
    } else {
      // 信用卡直接使用余额（可以是正数存款或负数欠款）
      editBalance = card.balance
      editDebt = 0  // 不再使用欠款字段
      editLimit = card.credit

      // 完全恢复信用卡日期设置
      editHasBillDaySet = card.billDay != nil
      editBillingDay = card.billDay
      editHasDueDaySet = card.dueDay != nil
      editRepaymentDay = card.dueDay
      editRepaymentType = card.isFixedDueDay ? .fixedDay : .afterBilling
    }
  }

  /// 获取两种货币之间的汇率
  private func getCurrencyRate(
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    // 使用CurrencyService的正确汇率计算方法
    return CurrencyService.shared.getCurrencyRate(
      from: sourceCurrency,
      to: targetCurrency,
      currencies: currencies
    )
  }

  /// 将金额从一种货币转换为另一种货币
  private func convertAmount(
    amount: Double,
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    let rate = getCurrencyRate(from: sourceCurrency, to: targetCurrency, currencies: currencies)
    return amount * rate
  }

  /// 判断交易是否为基准点(创建或调整类型)
  private func isBasePointTransaction(_ transaction: TransactionModel) -> Bool {
    return transaction.transactionType == .createCard || transaction.transactionType == .adjustCard
  }

  /// 找出指定日期之前的最近基准点交易
  private func findLastBasePoint(
    before date: Date,
    card: CardModel,
    transactions: [TransactionModel]
  ) -> TransactionModel? {
    // 筛选当前卡片的所有基准点交易
    let basePoints = transactions.filter {
      isBasePointTransaction($0) && ($0.fromCardId == card.id || $0.toCardId == card.id)
        && $0.transactionDate <= date
    }

    // 按日期降序排序，找出最近的基准点
    return basePoints.sorted { $0.transactionDate > $1.transactionDate }.first
  }

  /// 计算交易的影响金额(已转换为卡片货币)
  private func calculateTransactionImpact(_ transaction: TransactionModel, card: CardModel)
    -> Double
  {
    let type = transaction.transactionType
    let baseAmount = abs(transaction.transactionAmount)
    let discountAmount = transaction.discountAmount ?? 0

    // 如果是调整类型，直接返回调整金额
    if type == .adjustCard {
      return transaction.currency == card.currency
        ? transaction.transactionAmount
        : transaction.transactionAmount  // 暂时省略汇率转换
    }

    // 如果是创建类型，直接返回创建金额
    if type == .createCard {
      return transaction.currency == card.currency
        ? baseAmount : baseAmount  // 暂时省略汇率转换
    }

    // 计算其他交易类型的影响
    var impact: Double = 0

    switch type {
    case .expense:
      if transaction.fromCardId == card.id {
        // 计算实际支出金额（交易金额 - 优惠金额）
        let actualExpense = baseAmount - discountAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualExpense,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        impact = -convertedAmount
      }
    case .income:
      if transaction.toCardId == card.id {
        // 计算实际收入金额（交易金额 - 优惠金额）
        let actualIncome = baseAmount - discountAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualIncome,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    case .transfer:
      // 实际转账金额 = 交易金额 - 优惠金额（如果有的话）
      let actualTransferAmount = baseAmount - discountAmount

      if transaction.fromCardId == card.id {
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualTransferAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        impact = -convertedAmount
      } else if transaction.toCardId == card.id {
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualTransferAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    case .refund:
      if transaction.toCardId == card.id {
        // 退款交易：使用退款金额或交易金额
        let refundAmount = transaction.refundAmount ?? baseAmount
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: refundAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
        impact = convertedAmount
      }
    default:
      break
    }

    return impact
  }

  // MARK: - 交易记录管理

  /// 为新创建的卡片创建初始交易记录
  private func createInitialTransaction(for card: CardModel, modelContext: ModelContext) {
    let initialTransaction = TransactionModel(
      id: UUID(),
      transactionType: .createCard,
      transactionCategoryId: "SYS_CREATE_CARD",
      toCardId: card.id,  // 创建类型交易使用toCardId
      transactionAmount: card.balance,
      currency: card.currency,
      symbol: card.symbol,
      expenseToCardRate: 1.0,
      expenseToBaseRate: 1.0,
      incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0,
      isStatistics: card.isStatistics,
      remark: "创建卡片",
      transactionDate: card.createdAt,
      createdAt: Date(),
      updatedAt: Date()
    )

    do {
      modelContext.insert(initialTransaction)
      try modelContext.save()
      print("[交易创建] 成功为卡片 \(card.name) 创建初始交易记录")
    } catch {
      print("[交易创建] 创建初始交易记录失败: \(error.localizedDescription)")
    }
  }

  /// 创建调整交易记录（当卡片余额发生变化时）
  private func createAdjustmentTransaction(
    card: CardModel,
    amount: Double,
    currencyCode: String,
    modelContext: ModelContext
  ) {
    let transaction = TransactionModel(
      id: UUID(),
      transactionType: .adjustCard,
      transactionCategoryId: "SYS_ADJUST_CARD",
      toCardId: card.id,  // 调整类型交易也使用toCardId
      transactionAmount: amount,
      currency: currencyCode,
      symbol: editCurrencySymbol,
      expenseToCardRate: 1.0,
      expenseToBaseRate: 1.0,
      incomeToCardRate: 1.0,
      incomeToBaseRate: 1.0,
      isStatistics: true,
      remark: "余额调整",
      transactionDate: Date(),
      createdAt: Date(),
      updatedAt: Date()
    )

    do {
      modelContext.insert(transaction)
      try modelContext.save()
      print("[交易创建] 成功为卡片 \(card.name) 创建调整交易记录")
    } catch {
      print("[交易创建] 创建调整交易记录失败: \(error.localizedDescription)")
    }
  }

  /// 检查删除卡片时转账交易的完整性
  /// - Parameters:
  ///   - cardToDelete: 要删除的卡片
  ///   - allTransactions: 所有交易记录
  ///   - relatedTransactions: 与该卡片相关的交易记录
  /// - Returns: 会变成无效状态的转账交易列表
  private func checkTransferIntegrityForDeletion(
    cardToDelete: CardModel,
    allTransactions: [TransactionModel],
    relatedTransactions: [TransactionModel]
  ) -> [TransactionModel] {
    var problematicTransfers: [TransactionModel] = []

    // 查找所有转账交易
    let transferTransactions = allTransactions.filter { transaction in
      transaction.transactionType == .transfer
    }

    for transfer in transferTransactions {
      // 检查这个转账是否涉及要删除的卡片
      let involvesCardToDelete =
        transfer.fromCardId == cardToDelete.id || transfer.toCardId == cardToDelete.id

      if involvesCardToDelete {
        // 检查转账的另一方卡片是否仍然存在
        let otherCardId: UUID?
        if transfer.fromCardId == cardToDelete.id {
          otherCardId = transfer.toCardId
        } else {
          otherCardId = transfer.fromCardId
        }

        // 如果另一方卡片存在，那么删除当前卡片会导致转账变成无效状态
        if otherCardId != nil {
          // 这里我们假设其他卡片仍然存在（因为我们只是在删除一个卡片）
          // 所以这个转账会变成问题转账
          problematicTransfers.append(transfer)
        }
      }
    }

    return problematicTransfers
  }

  // MARK: - ActionSheet ViewModel Factory

  /// 创建卡片操作回调
  /// - Parameters:
  ///   - card: 要操作的卡片
  ///   - modelContext: 模型上下文
  ///   - closeDetailView: 关闭详情视图的回调
  /// - Returns: 包含所有操作回调的元组
  func createCardActionCallbacks(
    card: CardModel?,
    modelContext: ModelContext,
    closeDetailView: @escaping () -> Void
  ) -> (
    onEditCard: () -> Void,
    onDeleteWithTransactions: () -> Void,
    onDeleteCardOnly: () -> Void,
    dismiss: () -> Void
  ) {
    return (
      onEditCard: { [weak self] in
        self?.showCardActionSheet = false
        self?.toggleCardFlip()
      },
      onDeleteWithTransactions: { [weak self] in
        guard let card = card else { return }
        self?.deleteCardWithTransactions(card: card, modelContext: modelContext)
        closeDetailView()
      },
      onDeleteCardOnly: { [weak self] in
        guard let card = card else { return }
        self?.deleteCardOnly(card: card, modelContext: modelContext)
        closeDetailView()
      },
      dismiss: { [weak self] in
        self?.showCardActionSheet = false
      }
    )
  }
}
