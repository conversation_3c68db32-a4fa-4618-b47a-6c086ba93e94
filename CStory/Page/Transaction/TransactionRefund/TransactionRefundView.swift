//
//  TransactionRefundView.swift
//  CStory
//
//  Created by NZUE on 2025/3/10.
//

import SwiftData
import SwiftUI

struct TransactionRefundView: View {
  @Environment(\.dismiss) private var dismiss
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // 使用 ViewModel 管理业务逻辑
  @ObservedObject private var viewModel: TransactionRefundVM

  // MARK: - Sheet 状态管理
  @State private var isCardSheetPresented = false
  @State private var showingNumericKeypad = false
  @State private var isTimeSheetPresented = false

  init(transaction: TransactionModel, dataManager: DataManagement) {
    self.viewModel = TransactionRefundVM(
      transaction: transaction,
      dataManager: dataManager
    )
  }

  // MARK: - 顶部区域
  private var topArea: some View {
    VStack(spacing: 8) {
      // 退款标题
      HStack(spacing: 12) {
        Text("退款申请")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.cBlack)
          .frame(maxWidth: .infinity, alignment: .leading)

        HStack(spacing: 4) {
          Circle()
            .frame(width: 6, height: 6)
            .foregroundColor(.cAccentGreen)
          Text("退款")
            .font(.system(size: 13))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }

      // 退款时间
      HStack(spacing: 12) {
        Text(DateFormattingHelper.shared.smartFormat(date: viewModel.refundDate))
          .font(.system(size: 15))
          .foregroundColor(.cBlack.opacity(0.6))

        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          isTimeSheetPresented = true
        }) {
          Image("highlight")
            .font(.system(size: 16))
            .foregroundColor(.cAccentBlue)
        }

        Spacer()
      }
    }
    .padding(.vertical, 24)
    .padding(.horizontal, 16)
  }

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        /// 导航栏
        NavigationBarKit(
          viewModel: NavigationBarKitVM(
            title: "退款申请",
            backAction: {
              dismiss()
            },
            rightButton: .icon(
              "checkmark-2",
              style: viewModel.canSave ? .primary : .disabled,
              action: {
                if viewModel.canSave {
                  dataManager.hapticManager.trigger(.success)
                  handleRefundSave()
                }
              }
            )
          ))

        // 顶部区域
        topArea

        // 主要内容区域
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {

            // 原始交易信息区域
            DividerTitleKit(title: "原始交易")
              .padding(.horizontal, 16)

            Button(action: {
              dataManager.hapticManager.trigger(.impactLight)
              dismiss()
            }) {
              HStack(spacing: 12) {
                // 分类图标
                IconView(
                  viewModel: IconViewVM(
                    icon: viewModel.categoryIcon,
                    size: 40,
                    fontSize: 20,
                    backgroundColor: .clear,
                    cornerRadius: 12
                  ))

                VStack(alignment: .leading, spacing: 4) {
                  Text(viewModel.originalCategoryName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.cBlack)
                  Text(
                    DateFormattingHelper.shared.smartFormat(
                      date: viewModel.transaction.transactionDate)
                  )
                  .font(.system(size: 13))
                  .foregroundColor(.cBlack.opacity(0.6))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                  DisplayCurrencyView.size14(
                    symbol: viewModel.transaction.symbol,
                    amount: abs(viewModel.transaction.transactionAmount)
                  )
                  .weight(symbol: .medium, integer: .medium)

                  Text(viewModel.originalCardName)
                    .font(.system(size: 13))
                    .foregroundColor(.cBlack.opacity(0.6))
                }
              }
              .padding(.horizontal, 24)

            }
            .buttonStyle(PlainButtonStyle())

            // 退款金额区域
            DividerTitleKit(title: "退款金额")
              .padding(.horizontal, 16)

            TransactionAmountRowView(
              title: "退款金额",
              currencySymbol: viewModel.transaction.symbol,
              amount: Double(viewModel.refundAmount) ?? 0,
              isTotal: false,
              isEditing: true,
              onEditTap: {
                dataManager.hapticManager.trigger(.selection)
                viewModel.openNumericKeypad()
                showingNumericKeypad = true
              }
            )
            .padding(.horizontal, 24)

            // 退款卡片区域
            DividerTitleKit(title: "退款卡片")
              .padding(.horizontal, 16)

            TransactionCardView.withCardId(
              viewModel.selectedCardId,
              from: dataManager.cards,
              cardType: "退款卡片",
              isEditing: true,
              onEditCard: {
                dataManager.hapticManager.trigger(.selection)
                isCardSheetPresented = true
              }
            )

            Spacer()
          }
        }
        .background(.cWhite.opacity(0.5))
        .clipShape(
          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
        )
        .edgesIgnoringSafeArea(.bottom)

      }
    }
    .background(
      DottedGridBackground()
        .ignoresSafeArea(.all)
    )
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)

    // 浮动面板
    .floatingSheet(
      isPresented: $isCardSheetPresented,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCardSheet(
        viewModel: SelectCardSheetVM(
          selectedCardId: $viewModel.selectedCardId,
          cards: dataManager.cards,
          dataManager: dataManager,
          onCardSelected: { cardId in
            dataManager.hapticManager.trigger(.selection)
            viewModel.selectedCardId = cardId
            isCardSheetPresented = false
          },
          onCancel: {
            dataManager.hapticManager.trigger(.impactLight)
            isCardSheetPresented = false
          }
        )
      )
    }
    .floatingSheet(
      isPresented: $showingNumericKeypad,
      config: SheetBase(
        maxDetent: .height(314),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      NumericKeypad(
        text: $viewModel.numericKeypadText,
        onSave: {
          dataManager.hapticManager.trigger(.impactMedium)
          viewModel.saveNumericInput()
          showingNumericKeypad = false
        },
        allowNegative: false,  // 退款金额不允许负数
        maxDecimalPlaces: 2  // 普通金额使用2位小数
      )
      .background(.cBeige)
    }
    .floatingSheet(
      isPresented: $isTimeSheetPresented,
      config: SheetBase(
        maxDetent: .fraction(0.45),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectTimeSheet(
        viewModel: SelectTimeSheetVM(
          selectedDate: $viewModel.refundDate,
          onConfirm: {
            dataManager.hapticManager.trigger(.selection)
            isTimeSheetPresented = false
          },
          onCancel: {
            dataManager.hapticManager.trigger(.impactLight)
            isTimeSheetPresented = false
          }
        )
      )
    }
    .alert("操作失败", isPresented: $viewModel.showErrorAlert) {
      Button("确定", role: .cancel) {}
    } message: {
      Text(viewModel.errorMessage)
    }
  }

  // MARK: - 退款处理方法

  /// 处理退款保存
  private func handleRefundSave() {
    if viewModel.handleRefundSave(modelContext: modelContext) {
      // 触觉反馈
      dataManager.hapticManager.trigger(.success)
      // 关闭页面
      dismiss()
    }
  }

}

// MARK: - Preview Provider

#if DEBUG
  struct TransactionRefundView_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 场景1: 支出交易退款 - 正常情况
        TransactionRefundView(
          transaction: createExpenseTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("支出交易退款")

        // 场景2: 大额交易退款
        TransactionRefundView(
          transaction: createLargeAmountTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("大额交易退款")

        // 场景3: 美元交易退款（多币种）
        TransactionRefundView(
          transaction: createUSDTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("美元交易退款")

        // 场景4: 已有部分退款的交易
        TransactionRefundView(
          transaction: createPartialRefundTransaction(),
          dataManager: createMockDataManager()
        )
        .previewDisplayName("部分退款交易")
      }
    }

    // MARK: - Mock Data Creation Methods

    /// 创建普通支出交易（购物）
    private static func createExpenseTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "shopping_daily",
        fromCardId: UUID(uuidString: "********-1111-1111-1111-********1111")!,
        transactionAmount: 299.99,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "天猫购物 - 冬季外套，质量很好",
        transactionDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建大额支出交易
    private static func createLargeAmountTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "electronics",
        fromCardId: UUID(uuidString: "*************-2222-2222-************")!,
        transactionAmount: 8999.00,
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "MacBook Pro购买 - 工作需要",
        transactionDate: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建美元交易
    private static func createUSDTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "travel",
        fromCardId: UUID(uuidString: "*************-3333-3333-************")!,
        transactionAmount: 299.99,
        currency: "USD",
        symbol: "$",
        expenseToCardRate: 7.25,  // 美元到人民币汇率
        expenseToBaseRate: 7.25,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "美国出差 - 酒店住宿费用",
        transactionDate: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建已有部分退款的交易
    private static func createPartialRefundTransaction() -> TransactionModel {
      TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: "shopping_clothing",
        fromCardId: UUID(uuidString: "*************-4444-4444-************")!,
        transactionAmount: 599.99,
        refundAmount: 100.0,  // 已经退款100元
        currency: "CNY",
        symbol: "¥",
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: "服装购买 - 有一件不合适已退",
        transactionDate: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
        createdAt: Date(),
        updatedAt: Date()
      )
    }

    /// 创建模拟数据管理器
    private static func createMockDataManager() -> DataManagement {
      DataManagement(
        cards: createMockCards(),
        mainCategories: createMockCategories(),
        subCategories: [],
        currencies: createMockCurrencies(),
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }

    /// 创建模拟卡片数据
    private static func createMockCards() -> [CardModel] {
      [
        CardModel(
          id: UUID(uuidString: "********-1111-1111-1111-********1111")!,
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "招商银行信用卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 15000.0,
          credit: 20000.0,
          isStatistics: true,
          cover: "",
          bankLogo: nil,
          bankName: "招商银行",
          cardNumber: "1111",
          billDay: 5,
          isFixedDueDay: true,
          dueDay: 25,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(uuidString: "*************-2222-2222-************")!,
          order: 2,
          isCredit: false,
          isSelected: true,
          name: "支付宝余额",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 3000.0,
          credit: 0.0,
          isStatistics: true,
          cover: "",
          bankLogo: nil,
          bankName: "支付宝",
          cardNumber: "2222",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(uuidString: "*************-3333-3333-************")!,
          order: 3,
          isCredit: false,
          isSelected: true,
          name: "建设银行储蓄卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 25000.0,
          credit: 0.0,
          isStatistics: true,
          cover: "",
          bankLogo: nil,
          bankName: "建设银行",
          cardNumber: "3333",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(uuidString: "*************-4444-4444-************")!,
          order: 4,
          isCredit: false,
          isSelected: true,
          name: "微信钱包",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 1200.0,
          credit: 0.0,
          isStatistics: true,
          cover: "",
          bankLogo: nil,
          bankName: "微信",
          cardNumber: "4444",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]
    }

    /// 创建模拟分类数据
    private static func createMockCategories() -> [TransactionMainCategoryModel] {
      [
        TransactionMainCategoryModel(
          id: "shopping_daily",
          name: "日常购物",
          icon: .emoji("🛒"),
          order: 1,
          type: TransactionType.expense.rawValue
        ),
        TransactionMainCategoryModel(
          id: "electronics",
          name: "数码电器",
          icon: .emoji("💻"),
          order: 2,
          type: TransactionType.expense.rawValue
        ),
        TransactionMainCategoryModel(
          id: "travel",
          name: "差旅出行",
          icon: .emoji("✈️"),
          order: 3,
          type: TransactionType.expense.rawValue
        ),
        TransactionMainCategoryModel(
          id: "shopping_clothing",
          name: "服装配饰",
          icon: .emoji("👕"),
          order: 4,
          type: TransactionType.expense.rawValue
        ),
      ]
    }

    /// 创建模拟货币数据
    private static func createMockCurrencies() -> [CurrencyModel] {
      [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 1
        ),
        CurrencyModel(
          name: "美元",
          code: "USD",
          symbol: "$",
          rate: 7.25,
          order: 2
        ),
      ]
    }
  }
#endif
