//
//  iCloudSyncView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import CloudKit
import Network
import SwiftUI

/// iCloud 同步状态展示页面
///
/// 功能：
/// - 显示 iCloud 账户状态（已开启/未开启）
/// - 显示网络连接状态（WiFi/蜂窝数据/未连接）
/// - 提供 iCloud 同步相关说明信息
struct iCloudSyncView: View {

  // MARK: - Dependencies
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager: DataManagement
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - State Properties

  /// iCloud 是否开启
  @State private var iCloudEnabled = false

  /// 网络是否可用
  @State private var networkAvailable = true

  /// 网络连接类型
  @State private var connectionType = "检查中"

  /// 是否正在检查状态
  @State private var isCheckingStatus = true

  // MARK: - Private Properties

  /// 网络监控器
  private let networkMonitor = NWPathMonitor()

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "iCloud 同步",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            dismiss()
          }
        )
      )

      // 主要内容
      ScrollView {
        VStack(spacing: 16) {
          iCloudStatusSection
          networkStatusSection
          infoSection
        }
        .padding(.horizontal, 16)
        .padding(.top, 24)
        .padding(.bottom, 80)
      }
      .background(.cLightBlue)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      onAppear()
    }
    .onDisappear {
      onDisappear()
    }
  }

  // MARK: - UI Components

  /// iCloud 状态区域
  private var iCloudStatusSection: some View {
    StatusCardRow(
      leftIcon: "cloud",
      title: "iCloud 同步",
      subtitle: iCloudStatusDescription,
      rightContent: isCheckingStatus
        ? .loading
        : .status(
          text: iCloudEnabled ? "已开启" : "未开启",
          icon: iCloudEnabled ? "circle-check" : "circle-x",
          color: iCloudEnabled ? Color.green : Color.red
        )
    )
  }

  /// 网络状态区域
  private var networkStatusSection: some View {
    StatusCardRow(
      leftIcon: networkStatusIcon,
      title: "网络连接",
      subtitle: networkAvailable ? "通过 \(connectionType) 连接" : "网络连接异常",
      rightContent: .status(
        text: networkAvailable ? connectionType : "未连接",
        icon: networkAvailable ? "circle-check" : "circle-x",
        color: networkAvailable ? Color.green : Color.red
      )
    )
  }

  /// 说明信息区域
  private var infoSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text("关于iCloud同步")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()
      }

      VStack(alignment: .leading, spacing: 8) {
        infoItem("• 需要登录 iCloud 账户并开启网络连接")
        infoItem("• 数据将自动在您的所有设备间同步")
        infoItem("• 同步完全由系统后台管理")
        infoItem("• 删除应用会同时删除 iCloud 中的数据")
      }
    }
    .padding(16)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 信息项
  private func infoItem(_ text: String) -> some View {
    Text(text)
      .font(.system(size: 12, weight: .regular))
      .foregroundColor(.cBlack.opacity(0.6))
  }

  // MARK: - Computed Properties

  /// iCloud状态描述
  private var iCloudStatusDescription: String {
    if iCloudEnabled {
      return "数据将自动在设备间同步"
    } else {
      return "点击开启 iCloud 同步功能"
    }
  }

  /// iCloud状态颜色
  private var iCloudStatusColor: Color {
    return iCloudEnabled ? .green : .red
  }

  /// 网络状态图标
  private var networkStatusIcon: String {
    if !networkAvailable {
      return "wifi.slash"
    }

    switch connectionType {
    case "WiFi":
      return "wifi-full"
    case "蜂窝数据":
      return "live-full"
    case "有线网络":
      return "usb-c"
    default:
      return "chain-link-4"
    }
  }

  // MARK: - Lifecycle Methods

  /// 页面出现时的处理
  private func onAppear() {
    startNetworkMonitoring()
    checkiCloudStatus()
  }

  /// 页面消失时的处理
  private func onDisappear() {
    stopNetworkMonitoring()
  }

  // MARK: - Private Methods

  /// 开始网络监控
  private func startNetworkMonitoring() {
    let queue = DispatchQueue(label: "NetworkMonitor")

    networkMonitor.pathUpdateHandler = { path in
      DispatchQueue.main.async {
        let isConnected = (path.status == .satisfied)
        self.networkAvailable = isConnected

        if isConnected {
          self.connectionType = self.getConnectionType(from: path)
        } else {
          self.connectionType = "无连接"
        }

        print("网络状态: \(isConnected ? "已连接(\(self.connectionType))" : "未连接")")
      }
    }
    networkMonitor.start(queue: queue)
  }

  /// 停止网络监控
  private func stopNetworkMonitoring() {
    networkMonitor.cancel()
  }

  /// 检查 iCloud 状态
  private func checkiCloudStatus() {
    isCheckingStatus = true

    CKContainer.default().accountStatus { status, error in
      DispatchQueue.main.async {
        if let error = error {
          print("检查iCloud状态失败: \(error.localizedDescription)")
          self.iCloudEnabled = false
        } else {
          self.iCloudEnabled = (status == .available)
          print("iCloud状态: \(status == .available ? "已开启" : "未开启")")
        }

        self.isCheckingStatus = false
      }
    }
  }

  /// 获取连接类型
  private func getConnectionType(from path: NWPath) -> String {
    if path.usesInterfaceType(.wifi) {
      return "WiFi"
    } else if path.usesInterfaceType(.cellular) {
      return "蜂窝数据"
    } else if path.usesInterfaceType(.wiredEthernet) {
      return "有线网络"
    } else {
      return "其他网络"
    }
  }
}

// MARK: - Preview

#Preview {
  iCloudSyncView()
    .environment(\.dataManager, DataManagement())
    .environmentObject(PathManagerHelper())
}
