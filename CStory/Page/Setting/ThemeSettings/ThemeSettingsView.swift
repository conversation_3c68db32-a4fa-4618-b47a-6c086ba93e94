//
//  ThemeSettingsView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftUI

/// 主题设置页面
///
/// 提供主题模式选择功能的设置页面，采用MVVM架构模式。
/// 支持日间模式、夜间模式和系统自动三种主题选择。
///
/// ## 主要功能
/// - 主题模式选择（日间/夜间/系统自动）
/// - 实时主题预览
/// - 主题切换动画效果
/// - 触觉反馈集成
///
/// ## 设计特点
/// - 清晰的视觉层次和选项展示
/// - 流畅的交互动画
/// - 一致的设计语言
/// - 无障碍访问支持
///
/// ## 使用示例
/// ```swift
/// ThemeSettingsView(viewModel: ThemeSettingsVM(dataManager: dataManager))
/// ```
///
/// - Author: AI Assistant
/// - Since: 2025.8.13
/// - Note: 该视图专注于UI渲染，业务逻辑由ViewModel处理
struct ThemeSettingsView: View {

  // MARK: - Environment Properties

  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager

  // MARK: - State Properties

  /// 当前选中的主题模式，使用@AppStorage自动持久化
  @AppStorage(ThemeHelper.themeKey) private var selectedTheme: String = AppThemeMode.system.rawValue

  // MARK: - Computed Properties

  /// 当前主题模式枚举
  private var currentThemeMode: AppThemeMode {
    AppThemeMode(rawValue: selectedTheme) ?? .system
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "主题设置",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            dismiss()
          }
        )
      )

      // 主要内容区域
      VStack(spacing: 16) {
        // 主题切换行
        StatusCardRow(
          title: "主题切换",
          rightContent: .segmentedPicker(
            options: AppThemeMode.allCases.map { .icon($0.iconName) },
            selectedIndex: AppThemeMode.allCases.firstIndex(of: currentThemeMode) ?? 0,
            onSelect: { index in
              dataManager.hapticManager.trigger(.selection)
              selectedTheme = AppThemeMode.allCases[index].rawValue
            }
          )
        )

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
  }

}

// MARK: - Preview

#if DEBUG
  struct ThemeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
      ThemeSettingsView()
        .environment(\.dataManager, DataManagement())
    }
  }
#endif
