//
//  CategoryRow.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/24.
//

import SwiftUI

/// 交易类别设置行视图
///
/// 显示单个交易类别信息的可交互行组件，遵循MVVM架构模式。
/// 专用于设置页面的类别管理，通过CategorySettingRowVM处理所有数据格式化和业务逻辑。
///
/// 该组件支持显示类别图标、名称、子类别数量等信息，
/// 并提供展开/收起、操作菜单等交互功能。
///
/// ## 主要功能
/// - 类别图标和名称显示
/// - 子类别数量和状态指示
/// - 展开/收起交互
/// - 操作菜单触发
/// - 拖拽支持
///
/// ## 使用示例
/// ```swift
/// let viewModel = CategorySettingRowVM(from: categoryModel)
/// viewModel.onTap = { /* 处理点击 */ }
/// viewModel.onMoreAction = { /* 处理更多操作 */ }
/// CategorySettingRow(viewModel: viewModel)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.24
/// - Note: 该组件是无状态的，所有状态管理都在ViewModel中
struct CategorySettingRow: View {

  // MARK: - Properties

  /// 类别行视图模型
  ///
  /// 管理类别显示数据和交互逻辑的视图模型实例。
  /// 包含类别图标、名称、子类别数量、展开状态等所有显示相关数据。
  /// 视图通过观察此对象的变化来自动更新UI。
  @ObservedObject var viewModel: CategorySettingRowVM

  // MARK: - 主体视图

  var body: some View {
    Button(action: {
      viewModel.onTap?()
    }) {
      HStack(alignment: .center, spacing: 12) {
        // MARK: 左侧内容 (图标)
        IconView(
          viewModel: IconViewVM(
            icon: viewModel.icon,
            size: 40,
            fontSize: 20,
            backgroundColor: .clear,
            cornerRadius: 12
          )
        )

        // MARK: 中间内容 (类别信息)
        VStack(alignment: .leading, spacing: 4) {
          Text(viewModel.categoryName)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          Text(viewModel.subCategoryCountText)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        // MARK: 右侧内容 (操作按钮)
        HStack(spacing: 12) {
          Button(action: {
            viewModel.onMoreAction?()
          }) {
            Image("highlight")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cAccentBlue)

              .cornerRadius(8)
          }

          if viewModel.hasSubCategories {
            Image("chevron-right")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.cAccentBlue)
              .rotationEffect(.degrees(viewModel.isExpanded ? 90 : 0))
              .animation(.spring(response: 0.3), value: viewModel.isExpanded)
          }
        }
      }
      .frame(minHeight: 40)
      .padding(12)
      .background(.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(.cAccentBlue.opacity(0.2), lineWidth: 1)
      )
    }
  }
}

// MARK: - Preview Provider

/// 类别行组件预览
///
/// 提供多种典型类别场景的预览，展示组件在不同状态下的显示效果。
/// 包含有子类别、无子类别、展开状态等多种场景的视觉效果验证。
#if DEBUG
  struct CategoryRow_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 12) {
          Text("类别行预览")
            .font(.largeTitle)
            .bold()
            .padding()

          // 场景1: 有子类别的主类别
          CategorySettingRow(
            viewModel: .init(
              icon: .emoji("🛍️"),
              categoryName: "购物",
              subCategoryCount: 8,
              hasSubCategories: true,
              isExpanded: false
            ))

          // 场景2: 展开状态的类别
          CategorySettingRow(
            viewModel: .init(
              icon: .emoji("🍱"),
              categoryName: "餐饮",
              subCategoryCount: 5,
              hasSubCategories: true,
              isExpanded: true
            ))

          // 场景3: 无子类别的主类别
          CategorySettingRow(
            viewModel: .init(
              icon: .emoji("💰"),
              categoryName: "投资收益",
              subCategoryCount: 0,
              hasSubCategories: false,
              isExpanded: false
            ))

          // 场景4: 子类别较多的主类别
          CategorySettingRow(
            viewModel: .init(
              icon: .emoji("🚗"),
              categoryName: "交通",
              subCategoryCount: 12,
              hasSubCategories: true,
              isExpanded: false
            ))

        }
        .padding(.horizontal)
      }
      .background(Color("C_LightBlue"))
    }
  }
#endif
