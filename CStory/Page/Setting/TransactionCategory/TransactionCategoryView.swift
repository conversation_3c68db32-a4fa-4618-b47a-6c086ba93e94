//
//  TransactionCategorySettings.swift
//  CStory
//
//  Created by NZUE on 2025/1/12.
//

import SwiftData
import SwiftUI

/// 交易类别设置视图
///
/// 该视图用于管理交易的类别设置，包括收入和支出两大类别的管理。
/// 视图采用网格式布局，支持类别的查看和管理：
/// - 类型切换：支出/收入
/// - 主类别网格：以网格形式展示主类别
/// - 子类别网格：展示选中主类别的子类别
///
/// ## 主要功能
/// - 类型管理
///   - 支出类别管理
///   - 收入类别管理
/// - 类别操作
///   - 查看类别详情
///   - 编辑类别信息
///   - 添加新类别
///
/// ## 使用示例
/// ```swift
/// TransactionCategorySettings()
///     .modelContainer(for: TransactionMainCategoryModel.self)
/// ```
struct TransactionCategoryView: View {
  // MARK: - Properties
  @Environment(\.dismiss) private var dismiss
  /// 数据上下文 (仅用于写操作)
  @Environment(\.modelContext) private var modelContext
  /// 导航路径管理器
  @Environment(\.presentationMode) var presentationMode
  /// 路径管理器
  @EnvironmentObject private var pathManager: PathManagerHelper
  /// 集中式数据管理器
  @Environment(\.dataManager) private var dataManager

  /// ViewModel
  @ObservedObject private var viewModel: TransactionCategoryVM

  // MARK: - Initialization

  init(viewModel: TransactionCategoryVM? = nil) {
    if let viewModel = viewModel {
      self.viewModel = viewModel
    } else {
      // 创建默认的ViewModel（用于预览）
      self.viewModel = TransactionCategoryVM(
        dataManager: DataManagement(),
        modelContext: ModelContext(try! ModelContainer(for: TransactionMainCategoryModel.self))
      )
    }
  }

  /// 根据类型筛选的类别列表（直接使用dataManager数据）
  private var filteredCategories: [TransactionMainCategoryModel] {
    return dataManager.mainCategories.filter { $0.type == viewModel.selectedType.rawValue }
      .sorted { $0.order < $1.order }
  }

  // MARK: - UI Components

  /// 单个分类行组件
  private func categoryRow(for category: TransactionMainCategoryModel) -> some View {
    VStack(spacing: 0) {
      // 主类别行
      CategorySettingRow(
        viewModel: viewModel.createCategoryRowViewModel(for: category)
      )

      // 子类别展开区域
      if viewModel.shouldShowSubCategories(for: category) {
        let sortedSubCategories = viewModel.getSortedSubCategories(for: category)
        SubCategoriesExpandedView(
          subCategories: sortedSubCategories,
          parentCategory: category,
          viewModel: viewModel,
          hapticManager: dataManager.hapticManager,
          pathManager: pathManager,
          modelContext: modelContext,
          dataManager: dataManager
        )
      }
    }
  }

  // MARK: - Body

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        NavigationBarKit(
          viewModel: viewModel.createNavigationBarViewModel(
            onDismiss: {
              dataManager.hapticManager.trigger(.impactLight)
              dismiss()
            },
            onRemoveDuplicates: {
              viewModel.removeDuplicates()
            }
          )
        )
        SegmentedSelector(
          options: ["支出", "收入"],
          selectedIndex: Binding<Int>(
            get: { viewModel.selectedType == .expense ? 0 : 1 },
            set: { index in
              let newType: TransactionType = index == 0 ? .expense : .income
              viewModel.switchType(to: newType)
            }
          )
        )
        ScrollView(showsIndicators: false) {
          LazyVStack(spacing: 12) {
            ForEach(filteredCategories, id: \.id) { category in
              categoryRow(for: category)
            }
          }
          .padding(.horizontal, 16)
          .padding(.top, 12)
          .padding(.bottom, 80)
        }
      }
      FloatingActionButtonView(
        title: "添加主类别",
        action: {
          dataManager.hapticManager.trigger(.impactLight)
          pathManager.path.append(
            NavigationDestination.createTransactionCategory(
              isMainCategory: true,
              mainCategoryId: nil,
              selectedType: viewModel.selectedType
            )
          )
        },
        style: .primary
      )
    }
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .background(Color("C_LightBlue"))
    //    .background(.cLightBlue)
    .floatingSheet(
      isPresented: $viewModel.showingActionSheet,
      config: SheetBase(
        maxDetent: .fraction(0.5),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CategoryActionSheet(
        onEdit: {
          viewModel.handleEditAction(pathManager: pathManager)
        },
        onSort: {
          viewModel.handleSortAction(pathManager: pathManager)
        },
        onMigrate: {
          viewModel.handleMigrateAction()
        },
        onDelete: {
          viewModel.handleDeleteAction()
        },
        dismiss: {
          viewModel.dismissActionSheet()
        }
      )
    }
    .floatingSheet(
      isPresented: $viewModel.showingCategorySelector,
      config: SheetBase(
        maxDetent: .fraction(0.55),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      if let transaction = viewModel.tempTransaction,
        let sourceId = viewModel.migrationSourceCategoryId
      {
        SelectCategorySheet(
          viewModel: SelectCategorySheetVM(
            transactionType: transaction.transactionType,
            selectedCategoryId: .constant(nil),
            categories: dataManager.mainCategories,
            subCategories: dataManager.subCategories,
            dataManager: dataManager,
            onCategorySelected: { targetCategoryId in
              // 执行迁移逻辑 - 从源类别迁移到目标类别
              viewModel.migrateTransactions(from: sourceId, to: targetCategoryId)

              // 延迟一点再清理，确保数据保存完成
              DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                viewModel.completeMigration()
              }
            },
            onCancel: {
              viewModel.cancelMigration()
            }
          )
        )
      }
    }

    // 成功迁移提示
    .overlay {
      if viewModel.showMigrationSuccess {
        VStack {
          Spacer()

          HStack(spacing: 8) {
            Image("circle-check")
              .foregroundColor(.green)
            Text("已成功迁移 \(viewModel.migratedCount) 个交易")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
          }
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.white)
          .cornerRadius(20)
          .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
          .transition(.move(edge: .bottom).combined(with: .opacity))

          Spacer().frame(height: 100)
        }
      }
    }

    // 删除确认对话框（无交易）
    .alert("确认删除", isPresented: $viewModel.showDeleteConfirmation) {
      Button("取消", role: .cancel) {
        viewModel.cancelDelete()
      }
      Button("删除", role: .destructive) {
        if let categoryId = viewModel.categoryToDelete {
          viewModel.deleteCategory(categoryId)
        }
      }
    } message: {
      if let categoryId = viewModel.categoryToDelete {
        if let category = dataManager.mainCategories.first(where: { $0.id == categoryId }) {
          Text("确定要删除「\(category.name)」吗？此操作无法撤销。")
        } else if let subCategory = dataManager.subCategories.first(where: {
          $0.id == categoryId
        }) {
          Text("确定要删除「\(subCategory.name)」吗？此操作无法撤销。")
        }
      }
    }

    // 删除前迁移提示（有交易）
    .alert("该类别有关联交易", isPresented: $viewModel.showDeleteMigrationAlert) {
      Button("取消", role: .cancel) {
        viewModel.cancelDelete()
      }
      Button("迁移交易") {
        // 准备迁移
        if let categoryId = viewModel.categoryToDelete {
          viewModel.prepareMigrateTransactions(from: categoryId)
          viewModel.showDeleteMigrationAlert = false
        }
      }
      Button("直接删除", role: .destructive) {
        if let categoryId = viewModel.categoryToDelete {
          viewModel.deleteCategory(categoryId)
        }
      }
    } message: {
      Text(
        "该类别下有 \(viewModel.deleteAffectedTransactionCount) 个交易记录。您可以先将这些交易迁移到其他类别，或直接删除类别（交易记录将失去分类）。"
      )
    }

    // 有子类别提示
    .alert("无法删除", isPresented: $viewModel.showHasSubCategoriesAlert) {
      Button("确定") {
        viewModel.cancelDelete()
      }
    } message: {
      Text("该类别下还有子类别，请先删除所有子类别后再删除主类别。")
    }
  }
}

// MARK: - 拖拽插入指示器

/// 拖拽插入位置指示器
struct DragInsertionIndicator: View {
  var body: some View {
    Rectangle()
      .fill(.cAccentBlue)
      .frame(height: 3)
      .cornerRadius(1.5)
      .padding(.horizontal, 16)
      .padding(.vertical, 4)
      .shadow(color: .cAccentBlue.opacity(0.3), radius: 2, x: 0, y: 1)
  }
}

// MARK: - 子类别展开视图

/// 子类别展开视图
///
/// 当主类别被选中时显示的子类别横向滚动列表，
/// 参考手动交易页面的设计风格，提供独立的背景和圆角设计。
struct SubCategoriesExpandedView: View {
  let subCategories: [TransactionSubCategoryModel]
  let parentCategory: TransactionMainCategoryModel
  let viewModel: TransactionCategoryVM
  let hapticManager: HapticFeedbackManager
  let pathManager: PathManagerHelper
  let modelContext: ModelContext
  let dataManager: DataManagement

  var body: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      LazyHStack(spacing: 16) {
        ForEach(subCategories, id: \.id) { subCategory in
          SubCategoryItemView(
            subCategory: subCategory,
            isDragging: false,
            onTap: {
              dataManager.hapticManager.trigger(.selection)
              viewModel.showSubCategoryActions(categoryId: subCategory.id)
            }
          )
        }

        AddSubCategoryButton(
          parentCategoryId: parentCategory.id,
          selectedType: viewModel.selectedType,
          hapticManager: dataManager.hapticManager,
          pathManager: pathManager
        )
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.top, 8)
  }
}

// MARK: - 子类别项视图

/// 单个子类别项视图
struct SubCategoryItemView: View {
  let subCategory: TransactionSubCategoryModel
  let isDragging: Bool
  let onTap: () -> Void

  var body: some View {
    VStack(spacing: 6) {
      IconView(
        viewModel: IconViewVM(
          icon: subCategory.icon,
          size: 44,
          fontSize: 22,
          backgroundColor: .clear,
          cornerRadius: 12
        )
      )

      Text(subCategory.name)
        .font(.system(size: 12, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.7))
        .lineLimit(1)
    }
    .frame(width: 60)
    .scaleEffect(isDragging ? 1.05 : 1.0)
    .opacity(isDragging ? 0.8 : 1.0)
    .contentShape(Rectangle())  // 确保整个区域可点击
    .onTapGesture {
      onTap()
    }
  }
}

// MARK: - 添加子类别按钮

/// 添加子类别按钮
struct AddSubCategoryButton: View {
  let parentCategoryId: String
  let selectedType: TransactionType
  let hapticManager: HapticFeedbackManager
  let pathManager: PathManagerHelper

  var body: some View {
    VStack(spacing: 6) {
      Button(action: {
        hapticManager.trigger(.impactLight)
        pathManager.path.append(
          NavigationDestination.createTransactionCategory(
            isMainCategory: false,
            mainCategoryId: parentCategoryId,
            selectedType: selectedType
          )
        )
      }) {
        Image("plus-large")
          .font(.system(size: 22, weight: .medium))
          .foregroundColor(.cAccentBlue)
          .frame(width: 44, height: 44)
          .background(.cAccentBlue.opacity(0.05))
          .cornerRadius(12)
          .overlay(
            RoundedRectangle(cornerRadius: 12)
              .strokeBorder(.cAccentBlue.opacity(0.1), lineWidth: 1)
          )
      }

      Text("添加")
        .font(.system(size: 12, weight: .regular))
        .foregroundColor(.cBlack.opacity(0.7))
    }
    .frame(width: 60)
  }
}

// MARK: - 拖拽预览视图

/// 主类别拖拽预览视图
struct CategoryDragPreview: View {
  let category: TransactionMainCategoryModel

  var body: some View {
    VStack(spacing: 8) {
      IconView(
        viewModel: IconViewVM(
          icon: category.icon,
          size: 48,
          fontSize: 24,
          backgroundColor: .cAccentBlue.opacity(0.1),
          cornerRadius: 14
        ))

      Text(category.name)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.cBlack)
        .lineLimit(1)
        .padding(.horizontal, 8)
    }
    .padding(16)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .shadow(color: .cBlack.opacity(0.15), radius: 8, x: 0, y: 4)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.2), lineWidth: 1.5)
    )
  }
}

#if DEBUG
  struct TransactionCategoryView_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 场景1: 基础场景 - 包含主分类和子分类
        TransactionCategoryView()
          .withDataManager(createBasicDataManager())
          .previewDisplayName("基础场景")

        // 场景2: 丰富数据场景 - 多个主分类和子分类
        TransactionCategoryView()
          .withDataManager(createRichDataManager())
          .previewDisplayName("丰富数据场景")

        // 场景3: 空状态预览
        TransactionCategoryView()
          .withDataManager(createEmptyDataManager())
          .previewDisplayName("空状态")
      }
    }

    /// 创建基础预览数据
    static func createBasicDataManager() -> DataManagement {
      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "expense_food",
          name: "餐饮",
          icon: .emoji("🍽️"),
          order: 1,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "工资收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "shopping_clothes",
          name: "服装",
          icon: .emoji("👕"),
          order: 1,
          mainId: "expense_shopping"
        ),
      ]

      return DataManagement(
        cards: [],
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }

    /// 创建丰富数据预览
    static func createRichDataManager() -> DataManagement {
      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "expense_food",
          name: "餐饮",
          icon: .emoji("🍽️"),
          order: 1,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "expense_transport",
          name: "交通",
          icon: .emoji("🚗"),
          order: 2,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "工资收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "shopping_clothes",
          name: "服装",
          icon: .emoji("👕"),
          order: 1,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "food_restaurant",
          name: "餐厅",
          icon: .emoji("🏪"),
          order: 0,
          mainId: "expense_food"
        ),
        TransactionSubCategoryModel(
          id: "food_takeout",
          name: "外卖",
          icon: .emoji("🥡"),
          order: 1,
          mainId: "expense_food"
        ),
      ]

      return DataManagement(
        cards: [],
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }

    /// 创建空状态预览数据
    static func createEmptyDataManager() -> DataManagement {
      return DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }
  }
#endif
